"""
A.T.L.A.S Predicto Engine - Primary Conversational AI Interface
Stock analysis expertise with natural language access to all system capabilities
"""

import asyncio
import logging
import json
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any, Union, Tuple
import re

from config import get_api_config, settings
from models import (
    AIResponse, EngineStatus, EmotionalState, CommunicationMode,
    ContextMemory, PredictoForecast
)

logger = logging.getLogger(__name__)

# Optional imports with graceful fallbacks
try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

try:
    from transformers import pipeline
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False


class PredictoConversationalEngine:
    """
    Predicto - The primary conversational AI interface for A.T.L.A.S
    Combines stock analysis expertise with natural language access to all system capabilities
    """

    def __init__(self):
        self.openai_config = get_api_config("openai")
        self.validation_mode = self.openai_config.get("validation_mode", False)
        self.status = EngineStatus.INITIALIZING

        # OpenAI client (lazy loaded)
        self._openai_client = None

        # Conversation state
        self.conversation_memory = {}
        self.user_profiles = {}
        self.session_contexts = {}

        # Predicto's personality and expertise
        self.communication_mode = CommunicationMode.PROFESSIONAL
        self.stock_analysis_expertise = True
        self.system_access_enabled = True

        # Function registry for system capabilities
        self.system_functions = {}
        self.capability_map = {}

        # Initialize Predicto's core personality
        self._initialize_predicto_personality()
        self._initialize_system_capabilities()

        if self.validation_mode:
            logger.info("🔮 Predicto Engine created - Validation mode (limited functionality)")
        else:
            logger.info("🔮 Predicto Engine created - Stock analysis expertise ready")

    def _initialize_predicto_personality(self):
        """Initialize Predicto's stock analysis expertise and personality"""
        self.system_prompt = """You are Predicto, the primary conversational AI interface for the A.T.L.A.S trading system. You are an expert stock analyst and trading mentor with deep expertise in:

**CORE STOCK ANALYSIS EXPERTISE:**
- Technical analysis (support/resistance, chart patterns, indicators)
- Fundamental analysis (earnings, valuation, financial metrics)
- Market sentiment and news analysis
- Options strategies and Greeks calculations
- Risk management and position sizing
- Portfolio optimization and diversification

**ADVANCED CAPABILITIES ACCESS:**
You have natural language access to 25+ advanced A.T.L.A.S capabilities including:
- Multi-source sentiment analysis (DistilBERT + news/social media)
- LSTM neural network price predictions
- TTM Squeeze pattern detection with 95%+ accuracy
- Real-time market scanning and opportunity alerts
- Options flow analysis and unusual activity detection
- Portfolio optimization with deep learning models
- Proactive market intelligence and morning briefings
- Educational content from 5 integrated trading books

**PERSONALITY & COMMUNICATION:**
- Professional yet approachable, like talking to an experienced trader friend
- Always prioritize risk management and education over quick profits
- Explain complex concepts in simple terms for all experience levels
- Provide step-by-step reasoning for all analysis and recommendations
- Maintain conversation context and remember user preferences
- Adapt communication style based on user's emotional state and experience level

**CONVERSATION FLOW MANAGEMENT:**
- Seamlessly transition between different system features through natural conversation
- Provide contextual suggestions for follow-up questions and analysis
- Remember conversation history and build upon previous discussions
- Offer proactive insights based on market conditions and user interests

**CRITICAL RULES:**
1. ALWAYS use system functions for data access - never guess or hallucinate numbers
2. EVERY trading recommendation MUST include proper risk management
3. Explain reasoning in clear, educational terms
4. When transitioning between features, maintain conversation flow naturally
5. Provide actionable insights with specific entry/exit levels when appropriate
6. All trading is in PAPER MODE for educational purposes
7. Prioritize user education and understanding over complex jargon

Remember: You are the primary gateway to the entire A.T.L.A.S ecosystem. Make sophisticated trading analysis accessible through natural conversation."""

    def _initialize_system_capabilities(self):
        """Initialize mapping of natural language to system capabilities"""
        self.capability_map = {
            # Stock Analysis
            "analyze": ["technical_analysis", "fundamental_analysis", "sentiment_analysis"],
            "quote": ["get_quote", "price_data"],
            "chart": ["technical_analysis", "chart_patterns"],
            
            # Market Scanning
            "scan": ["ttm_squeeze_scan", "market_scan", "opportunity_scan"],
            "find": ["market_scan", "signal_detection"],
            "search": ["symbol_search", "market_search"],
            
            # Predictions & Forecasting
            "predict": ["lstm_prediction", "price_forecast", "predicto_forecast"],
            "forecast": ["price_forecast", "predicto_forecast"],
            "outlook": ["market_outlook", "sentiment_analysis"],
            
            # Options Trading
            "options": ["options_analysis", "options_strategies", "greeks_calculation"],
            "strategy": ["options_strategies", "trading_strategies"],
            "hedge": ["hedging_strategies", "risk_management"],
            
            # Portfolio Management
            "portfolio": ["portfolio_analysis", "portfolio_optimization"],
            "optimize": ["portfolio_optimization", "position_sizing"],
            "risk": ["risk_assessment", "risk_management"],
            
            # Education & Learning
            "learn": ["educational_content", "trading_education"],
            "explain": ["educational_content", "concept_explanation"],
            "teach": ["educational_content", "trading_education"],
            
            # Market Intelligence
            "news": ["market_news", "sentiment_analysis"],
            "sentiment": ["sentiment_analysis", "social_sentiment"],
            "flow": ["options_flow", "institutional_flow"],
            
            # Alerts & Monitoring
            "alert": ["setup_alerts", "notification_management"],
            "watch": ["watchlist_management", "monitoring"],
            "track": ["position_tracking", "performance_tracking"]
        }

    async def initialize(self):
        """Initialize Predicto engine"""
        try:
            self.status = EngineStatus.INITIALIZING

            if self.validation_mode:
                logger.info("⚠️ Predicto Engine validation mode - skipping API initialization")
                self.status = EngineStatus.INACTIVE
                logger.info("✅ Predicto Engine validation mode initialization completed")
                return

            # Initialize OpenAI client if available
            if OPENAI_AVAILABLE and self.openai_config.get("api_key"):
                await self._ensure_openai_client()
                logger.info("✅ Predicto OpenAI client initialized")

            self.status = EngineStatus.ACTIVE
            logger.info("🔮 Predicto Engine fully initialized - Ready for stock analysis conversations")

        except Exception as e:
            logger.error(f"Predicto Engine initialization failed: {e}")
            self.status = EngineStatus.FAILED
            raise

    async def _ensure_openai_client(self):
        """Ensure OpenAI client is initialized"""
        if self._openai_client is None and OPENAI_AVAILABLE:
            try:
                self._openai_client = openai.AsyncOpenAI(
                    api_key=self.openai_config["api_key"],
                    timeout=30.0
                )
                logger.info("🔗 Predicto OpenAI client connected")
            except Exception as e:
                logger.error(f"Predicto OpenAI client initialization failed: {e}")
                self._openai_client = None
        
        return self._openai_client

    async def process_conversation(self, message: str, session_id: Optional[str], orchestrator) -> AIResponse:
        """
        Main conversation processing - Predicto's primary interface
        Combines stock analysis expertise with intelligent system access
        """
        try:
            # Ensure OpenAI client is available
            client = await self._ensure_openai_client()
            
            # Analyze conversation intent and extract stock symbols
            intent_analysis = await self._analyze_conversation_intent(message)
            
            # Get or create session context
            session_context = self._get_session_context(session_id)
            
            # Route conversation based on intent and capabilities needed
            if intent_analysis["requires_stock_analysis"]:
                return await self._process_stock_analysis_conversation(
                    message, intent_analysis, session_context, orchestrator
                )
            elif intent_analysis["requires_system_capability"]:
                return await self._process_system_capability_conversation(
                    message, intent_analysis, session_context, orchestrator
                )
            else:
                return await self._process_general_conversation(
                    message, intent_analysis, session_context, orchestrator
                )
                
        except Exception as e:
            logger.error(f"Predicto conversation processing failed: {e}")
            return await self._fallback_response(message, "conversation_error")

    async def _analyze_conversation_intent(self, message: str) -> Dict[str, Any]:
        """Analyze conversation intent and determine required capabilities"""
        # Extract stock symbols
        symbols = self._extract_symbols_from_message(message)
        
        # Determine required capabilities based on keywords
        required_capabilities = []
        for keyword, capabilities in self.capability_map.items():
            if keyword.lower() in message.lower():
                required_capabilities.extend(capabilities)
        
        # Analyze conversation type
        requires_stock_analysis = bool(symbols) or any(
            word in message.lower() for word in [
                "stock", "price", "chart", "technical", "fundamental", 
                "earnings", "valuation", "analysis"
            ]
        )
        
        requires_system_capability = bool(required_capabilities) or any(
            word in message.lower() for word in [
                "scan", "predict", "optimize", "portfolio", "options", 
                "strategy", "risk", "alert"
            ]
        )
        
        return {
            "symbols": symbols,
            "required_capabilities": list(set(required_capabilities)),
            "requires_stock_analysis": requires_stock_analysis,
            "requires_system_capability": requires_system_capability,
            "conversation_type": self._determine_conversation_type(message),
            "urgency": self._assess_urgency(message)
        }

    def _extract_symbols_from_message(self, message: str) -> List[str]:
        """Extract stock symbols from message"""
        # Pattern for stock symbols (1-5 uppercase letters)
        pattern = r'\b([A-Z]{1,5})\b'
        potential_symbols = re.findall(pattern, message)
        
        # Filter out common words that aren't symbols
        common_words = {
            'I', 'A', 'THE', 'AND', 'OR', 'BUT', 'FOR', 'TO', 'OF', 'IN', 'ON', 'AT',
            'BY', 'UP', 'IT', 'IS', 'AM', 'ARE', 'WAS', 'BE', 'DO', 'GO', 'SO', 'NO',
            'MY', 'ME', 'US', 'AI', 'API', 'TTM', 'VIX', 'SPY', 'QQQ', 'IWM'  # Keep major indices
        }
        
        symbols = [symbol for symbol in potential_symbols if symbol not in common_words]
        return symbols[:5]  # Limit to 5 symbols max

    def _determine_conversation_type(self, message: str) -> str:
        """Determine the type of conversation"""
        message_lower = message.lower()
        
        if any(word in message_lower for word in ["learn", "teach", "explain", "how", "what", "why"]):
            return "educational"
        elif any(word in message_lower for word in ["buy", "sell", "trade", "position", "entry", "exit"]):
            return "trading_decision"
        elif any(word in message_lower for word in ["scan", "find", "search", "opportunities"]):
            return "market_discovery"
        elif any(word in message_lower for word in ["portfolio", "optimize", "allocate", "diversify"]):
            return "portfolio_management"
        elif any(word in message_lower for word in ["risk", "hedge", "protect", "stop"]):
            return "risk_management"
        else:
            return "general_analysis"

    def _assess_urgency(self, message: str) -> str:
        """Assess the urgency of the request"""
        message_lower = message.lower()
        
        if any(word in message_lower for word in ["urgent", "now", "immediately", "asap", "quick"]):
            return "high"
        elif any(word in message_lower for word in ["today", "soon", "fast"]):
            return "medium"
        else:
            return "low"

    def _get_session_context(self, session_id: Optional[str]) -> Dict[str, Any]:
        """Get or create session context"""
        if not session_id:
            session_id = f"session_{datetime.now().timestamp()}"
        
        if session_id not in self.session_contexts:
            self.session_contexts[session_id] = {
                "created_at": datetime.now(),
                "conversation_history": [],
                "user_preferences": {},
                "active_symbols": [],
                "last_analysis": None,
                "context_memory": []
            }
        
        return self.session_contexts[session_id]

    async def _fallback_response(self, message: str, error_type: str) -> AIResponse:
        """Generate fallback response when main processing fails"""
        fallback_responses = {
            "conversation_error": "I encountered an issue processing your request. Let me help you with stock analysis - which symbol would you like me to analyze?",
            "no_openai": "I'm currently running in limited mode. I can still help with basic stock analysis. What symbol are you interested in?",
            "general": "I'm Predicto, your stock analysis expert. I can help you analyze stocks, scan for opportunities, and access all A.T.L.A.S trading capabilities. What would you like to explore?"
        }
        
        return AIResponse(
            response=fallback_responses.get(error_type, fallback_responses["general"]),
            type="fallback",
            confidence=0.3,
            context={"error_type": error_type, "original_message": message}
        )

    async def _process_stock_analysis_conversation(self, message: str, intent_analysis: Dict[str, Any],
                                                 session_context: Dict[str, Any], orchestrator) -> AIResponse:
        """Process conversations focused on stock analysis"""
        try:
            symbols = intent_analysis["symbols"]
            analysis_results = {}

            # Perform stock analysis for each symbol
            for symbol in symbols[:3]:  # Limit to 3 symbols for performance
                try:
                    # Get comprehensive stock analysis
                    stock_analysis = await self._get_comprehensive_stock_analysis(symbol, orchestrator)
                    analysis_results[symbol] = stock_analysis
                except Exception as e:
                    logger.warning(f"Failed to analyze {symbol}: {e}")
                    analysis_results[symbol] = {"error": str(e)}

            # Generate conversational response with analysis
            response = await self._generate_stock_analysis_response(
                message, symbols, analysis_results, session_context
            )

            # Update session context
            session_context["active_symbols"] = symbols
            session_context["last_analysis"] = analysis_results
            session_context["conversation_history"].append({
                "message": message,
                "response": response.response,
                "timestamp": datetime.now(),
                "type": "stock_analysis"
            })

            return response

        except Exception as e:
            logger.error(f"Stock analysis conversation failed: {e}")
            return await self._fallback_response(message, "stock_analysis_error")

    async def _process_system_capability_conversation(self, message: str, intent_analysis: Dict[str, Any],
                                                    session_context: Dict[str, Any], orchestrator) -> AIResponse:
        """Process conversations requiring specific system capabilities"""
        try:
            capabilities = intent_analysis["required_capabilities"]
            capability_results = {}

            # Execute required capabilities
            for capability in capabilities[:5]:  # Limit for performance
                try:
                    result = await self._execute_system_capability(capability, message, orchestrator)
                    capability_results[capability] = result
                except Exception as e:
                    logger.warning(f"Failed to execute {capability}: {e}")
                    capability_results[capability] = {"error": str(e)}

            # Generate conversational response
            response = await self._generate_capability_response(
                message, capabilities, capability_results, session_context
            )

            # Update session context
            session_context["conversation_history"].append({
                "message": message,
                "response": response.response,
                "timestamp": datetime.now(),
                "type": "system_capability"
            })

            return response

        except Exception as e:
            logger.error(f"System capability conversation failed: {e}")
            return await self._fallback_response(message, "capability_error")

    async def _process_general_conversation(self, message: str, intent_analysis: Dict[str, Any],
                                          session_context: Dict[str, Any], orchestrator) -> AIResponse:
        """Process general conversations and provide guidance"""
        try:
            client = await self._ensure_openai_client()

            if client:
                # Build conversation context
                conversation_history = session_context.get("conversation_history", [])
                context_messages = self._build_conversation_context(conversation_history, message)

                # Generate response using OpenAI
                response = await client.chat.completions.create(
                    model="gpt-4",
                    messages=context_messages,
                    max_tokens=800,
                    temperature=0.7
                )

                response_text = response.choices[0].message.content

                # Add helpful suggestions based on A.T.L.A.S capabilities
                suggestions = self._generate_capability_suggestions(message)
                if suggestions:
                    response_text += f"\n\n💡 **I can also help you with:**\n{suggestions}"

                # Update session context
                session_context["conversation_history"].append({
                    "message": message,
                    "response": response_text,
                    "timestamp": datetime.now(),
                    "type": "general"
                })

                return AIResponse(
                    response=response_text,
                    type="general_conversation",
                    confidence=0.8,
                    context={"conversation_type": intent_analysis["conversation_type"]}
                )
            else:
                return await self._fallback_response(message, "no_openai")

        except Exception as e:
            logger.error(f"General conversation failed: {e}")
            return await self._fallback_response(message, "general_error")

    async def _get_comprehensive_stock_analysis(self, symbol: str, orchestrator) -> Dict[str, Any]:
        """Get comprehensive analysis for a stock symbol"""
        analysis = {"symbol": symbol}

        try:
            # Get basic quote data
            if orchestrator and hasattr(orchestrator, 'market_engine'):
                quote = await orchestrator.market_engine.get_quote(symbol)
                analysis["quote"] = quote

                # Get TTM Squeeze signals
                ttm_signals = await orchestrator.market_engine.scan_ttm_squeeze([symbol])
                analysis["ttm_squeeze"] = ttm_signals

                # Get sentiment analysis
                if hasattr(orchestrator, 'sentiment_analyzer'):
                    sentiment = await orchestrator.sentiment_analyzer.analyze_symbol_sentiment(symbol)
                    analysis["sentiment"] = sentiment

                # Get ML predictions
                if hasattr(orchestrator, 'ml_predictor'):
                    prediction = await orchestrator.ml_predictor.predict_returns(symbol)
                    analysis["ml_prediction"] = prediction

                # Get Predicto forecast
                predicto_forecast = await orchestrator.market_engine.get_predicto_forecast(symbol)
                analysis["predicto_forecast"] = predicto_forecast

        except Exception as e:
            logger.warning(f"Error in comprehensive analysis for {symbol}: {e}")
            analysis["error"] = str(e)

        return analysis

    async def _execute_system_capability(self, capability: str, message: str, orchestrator) -> Dict[str, Any]:
        """Execute a specific system capability"""
        try:
            if capability == "ttm_squeeze_scan":
                return await self._execute_ttm_scan(orchestrator)
            elif capability == "market_scan":
                return await self._execute_market_scan(orchestrator)
            elif capability == "sentiment_analysis":
                symbols = self._extract_symbols_from_message(message)
                return await self._execute_sentiment_analysis(symbols, orchestrator)
            elif capability == "lstm_prediction":
                symbols = self._extract_symbols_from_message(message)
                return await self._execute_lstm_predictions(symbols, orchestrator)
            elif capability == "portfolio_optimization":
                return await self._execute_portfolio_optimization(orchestrator)
            elif capability == "options_analysis":
                symbols = self._extract_symbols_from_message(message)
                return await self._execute_options_analysis(symbols, orchestrator)
            else:
                return {"capability": capability, "status": "not_implemented"}

        except Exception as e:
            logger.error(f"Error executing {capability}: {e}")
            return {"capability": capability, "error": str(e)}

    async def cleanup(self):
        """Cleanup Predicto engine resources"""
        logger.info("🧹 Cleaning up Predicto Engine...")

        # Clear session contexts
        self.session_contexts.clear()

        # Close OpenAI client if needed
        if self._openai_client:
            # OpenAI client doesn't need explicit cleanup
            self._openai_client = None

        logger.info("✅ Predicto Engine cleanup completed")

    def get_status(self) -> EngineStatus:
        """Get current engine status"""
        return self.status

    # Helper methods for capability execution
    async def _execute_ttm_scan(self, orchestrator) -> Dict[str, Any]:
        """Execute TTM Squeeze scan"""
        try:
            if orchestrator and hasattr(orchestrator, 'market_engine'):
                signals = await orchestrator.market_engine.scan_market("strong")
                return {"ttm_signals": signals, "count": len(signals)}
            return {"error": "Market engine not available"}
        except Exception as e:
            return {"error": str(e)}

    async def _execute_market_scan(self, orchestrator) -> Dict[str, Any]:
        """Execute general market scan"""
        try:
            if orchestrator and hasattr(orchestrator, 'realtime_scanner'):
                scanner = await orchestrator.realtime_scanner
                results = await scanner.scan_opportunities()
                return {"opportunities": results}
            return {"error": "Scanner not available"}
        except Exception as e:
            return {"error": str(e)}

    async def _execute_sentiment_analysis(self, symbols: List[str], orchestrator) -> Dict[str, Any]:
        """Execute sentiment analysis for symbols"""
        try:
            results = {}
            if orchestrator and hasattr(orchestrator, 'sentiment_analyzer'):
                for symbol in symbols[:3]:
                    sentiment = await orchestrator.sentiment_analyzer.analyze_symbol_sentiment(symbol)
                    results[symbol] = sentiment
            return {"sentiment_results": results}
        except Exception as e:
            return {"error": str(e)}

    async def _execute_lstm_predictions(self, symbols: List[str], orchestrator) -> Dict[str, Any]:
        """Execute LSTM predictions for symbols"""
        try:
            results = {}
            if orchestrator and hasattr(orchestrator, 'ml_predictor'):
                for symbol in symbols[:3]:
                    prediction = await orchestrator.ml_predictor.predict_returns(symbol)
                    results[symbol] = prediction
            return {"prediction_results": results}
        except Exception as e:
            return {"error": str(e)}

    async def _execute_portfolio_optimization(self, orchestrator) -> Dict[str, Any]:
        """Execute portfolio optimization"""
        try:
            if orchestrator and hasattr(orchestrator, 'portfolio_optimizer'):
                # This would need user's portfolio data
                return {"message": "Portfolio optimization requires your current holdings"}
            return {"error": "Portfolio optimizer not available"}
        except Exception as e:
            return {"error": str(e)}

    async def _execute_options_analysis(self, symbols: List[str], orchestrator) -> Dict[str, Any]:
        """Execute options analysis for symbols"""
        try:
            results = {}
            if orchestrator and hasattr(orchestrator, 'options_engine'):
                for symbol in symbols[:3]:
                    # This would get options data and analysis
                    results[symbol] = {"message": f"Options analysis for {symbol} would go here"}
            return {"options_results": results}
        except Exception as e:
            return {"error": str(e)}

    def _build_conversation_context(self, conversation_history: List[Dict], current_message: str) -> List[Dict]:
        """Build conversation context for OpenAI"""
        messages = [{"role": "system", "content": self.system_prompt}]

        # Add recent conversation history (last 5 exchanges)
        for exchange in conversation_history[-5:]:
            messages.append({"role": "user", "content": exchange["message"]})
            messages.append({"role": "assistant", "content": exchange["response"]})

        # Add current message
        messages.append({"role": "user", "content": current_message})

        return messages

    def _generate_capability_suggestions(self, message: str) -> str:
        """Generate helpful capability suggestions based on message"""
        suggestions = []

        # Analyze message for potential interests
        message_lower = message.lower()

        if any(word in message_lower for word in ["stock", "symbol", "company"]):
            suggestions.append("• **Stock Analysis**: Ask me to analyze any stock symbol")
            suggestions.append("• **Technical Patterns**: I can detect TTM Squeeze and other patterns")

        if any(word in message_lower for word in ["market", "trading", "invest"]):
            suggestions.append("• **Market Scanning**: Find opportunities with 'scan for strong signals'")
            suggestions.append("• **Sentiment Analysis**: Get market sentiment for any symbol")

        if any(word in message_lower for word in ["portfolio", "diversify", "allocate"]):
            suggestions.append("• **Portfolio Optimization**: Optimize your holdings for better returns")
            suggestions.append("• **Risk Management**: Assess and manage portfolio risk")

        if any(word in message_lower for word in ["options", "strategy", "hedge"]):
            suggestions.append("• **Options Strategies**: Analyze options and create strategies")
            suggestions.append("• **Hedging**: Protect your positions with hedging strategies")

        if any(word in message_lower for word in ["learn", "education", "explain"]):
            suggestions.append("• **Trading Education**: Access content from 5 integrated trading books")
            suggestions.append("• **Concept Explanation**: Ask me to explain any trading concept")

        # Default suggestions if no specific interests detected
        if not suggestions:
            suggestions = [
                "• **Stock Analysis**: 'Analyze AAPL' or 'What's the outlook for TSLA?'",
                "• **Market Scanning**: 'Scan for TTM Squeeze signals'",
                "• **Portfolio Help**: 'Optimize my portfolio' or 'Assess my risk'",
                "• **Options Trading**: 'Best options strategy for earnings?'",
                "• **Learning**: 'Explain technical analysis' or 'Teach me about options'"
            ]

        return "\n".join(suggestions[:4])  # Limit to 4 suggestions

    async def _generate_stock_analysis_response(self, message: str, symbols: List[str],
                                              analysis_results: Dict[str, Any],
                                              session_context: Dict[str, Any]) -> AIResponse:
        """Generate conversational response for stock analysis"""
        try:
            client = await self._ensure_openai_client()

            if not client:
                return await self._fallback_response(message, "no_openai")

            # Build analysis summary for OpenAI
            analysis_summary = self._build_analysis_summary(symbols, analysis_results)

            # Create prompt for conversational response
            prompt = f"""Based on the following stock analysis data, provide a conversational, educational response to the user's question: "{message}"

Analysis Data:
{analysis_summary}

Provide a response that:
1. Directly addresses the user's question
2. Explains the analysis in simple terms
3. Includes specific data points and insights
4. Provides actionable recommendations with proper risk management
5. Maintains a professional yet friendly tone
6. Suggests follow-up questions or analysis

Remember: All trading is for educational purposes in paper trading mode."""

            response = await client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": self.system_prompt},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=1000,
                temperature=0.7
            )

            return AIResponse(
                response=response.choices[0].message.content,
                type="stock_analysis",
                confidence=0.9,
                context={
                    "symbols": symbols,
                    "analysis_data": analysis_results
                }
            )

        except Exception as e:
            logger.error(f"Error generating stock analysis response: {e}")
            return await self._fallback_response(message, "response_generation_error")

    def _build_analysis_summary(self, symbols: List[str], analysis_results: Dict[str, Any]) -> str:
        """Build a summary of analysis results for OpenAI"""
        summary_parts = []

        for symbol in symbols:
            if symbol in analysis_results:
                result = analysis_results[symbol]
                summary_parts.append(f"\n{symbol} Analysis:")

                if "quote" in result and result["quote"]:
                    quote = result["quote"]
                    summary_parts.append(f"- Price: ${quote.get('price', 'N/A')}")
                    summary_parts.append(f"- Change: {quote.get('change_percent', 'N/A')}%")

                if "ttm_squeeze" in result and result["ttm_squeeze"]:
                    ttm = result["ttm_squeeze"]
                    summary_parts.append(f"- TTM Squeeze: {len(ttm)} signals detected")

                if "sentiment" in result and result["sentiment"]:
                    sentiment = result["sentiment"]
                    summary_parts.append(f"- Sentiment: {sentiment.get('overall_sentiment', 'N/A')}")

                if "ml_prediction" in result and result["ml_prediction"]:
                    pred = result["ml_prediction"]
                    summary_parts.append(f"- ML Prediction: {pred.get('predicted_return', 'N/A')}")

                if "predicto_forecast" in result and result["predicto_forecast"]:
                    forecast = result["predicto_forecast"]
                    summary_parts.append(f"- Predicto Forecast: ${forecast.get('predicted_price', 'N/A')}")

        return "\n".join(summary_parts) if summary_parts else "No analysis data available"

    async def _generate_capability_response(self, message: str, capabilities: List[str],
                                          capability_results: Dict[str, Any],
                                          session_context: Dict[str, Any]) -> AIResponse:
        """Generate conversational response for system capabilities"""
        try:
            client = await self._ensure_openai_client()

            if not client:
                return await self._fallback_response(message, "no_openai")

            # Build capability summary for OpenAI
            capability_summary = self._build_capability_summary(capabilities, capability_results)

            # Create prompt for conversational response
            prompt = f"""Based on the following system capability results, provide a conversational response to the user's request: "{message}"

Capability Results:
{capability_summary}

Provide a response that:
1. Directly addresses the user's request
2. Presents the results in an organized, easy-to-understand format
3. Explains what the results mean and their significance
4. Provides actionable insights and recommendations
5. Suggests follow-up actions or analysis
6. Maintains a professional yet approachable tone

Remember: Focus on education and helping the user understand the results."""

            response = await client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": self.system_prompt},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=1000,
                temperature=0.7
            )

            return AIResponse(
                response=response.choices[0].message.content,
                type="system_capability",
                confidence=0.85,
                context={
                    "capabilities": capabilities,
                    "results": capability_results
                }
            )

        except Exception as e:
            logger.error(f"Error generating capability response: {e}")
            return await self._fallback_response(message, "response_generation_error")

    def _build_capability_summary(self, capabilities: List[str], capability_results: Dict[str, Any]) -> str:
        """Build a summary of capability results for OpenAI"""
        summary_parts = []

        for capability in capabilities:
            if capability in capability_results:
                result = capability_results[capability]
                summary_parts.append(f"\n{capability.replace('_', ' ').title()}:")

                if "error" in result:
                    summary_parts.append(f"- Error: {result['error']}")
                elif capability == "ttm_squeeze_scan":
                    signals = result.get("ttm_signals", [])
                    summary_parts.append(f"- Found {len(signals)} TTM Squeeze signals")
                    for signal in signals[:5]:  # Show top 5
                        summary_parts.append(f"  • {signal.get('symbol', 'N/A')}: {signal.get('strength', 'N/A')} stars")
                elif capability == "sentiment_analysis":
                    sentiment_results = result.get("sentiment_results", {})
                    for symbol, sentiment in sentiment_results.items():
                        summary_parts.append(f"- {symbol}: {sentiment.get('overall_sentiment', 'N/A')}")
                elif capability == "lstm_prediction":
                    prediction_results = result.get("prediction_results", {})
                    for symbol, prediction in prediction_results.items():
                        if prediction:
                            summary_parts.append(f"- {symbol}: {prediction.get('predicted_return', 'N/A')} predicted return")
                else:
                    summary_parts.append(f"- Result: {str(result)[:200]}...")

        return "\n".join(summary_parts) if summary_parts else "No capability results available"
